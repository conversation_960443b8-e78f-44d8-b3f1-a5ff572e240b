-------------------------------------------------------------------------------
-- 1. DROP DATABASE IF EXISTS
-------------------------------------------------------------------------------
IF DB_ID(N'aiabimees') IS NOT NULL
BEGIN
    ALTER DATABASE [aiabimees] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE [aiabimees];
END
GO

-------------------------------------------------------------------------------
-- 2. CREATE DATABASE
-------------------------------------------------------------------------------
CREATE DATABASE [aiabimees];
GO

USE [aiabimees];
GO

-------------------------------------------------------------------------------
-- 3. CREATE TABLE: Users
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[users] (
    [user_id]   BIGINT         NOT NULL IDENTITY(1,1),
    [username]  NVARCHAR(191)  NOT NULL,
    [password]  NVARCHAR(191)  NOT NULL,
    [email]     NVARCHAR(191)  NULL,
    [created_at] DATETIME2(0)  NOT NULL CONSTRAINT [DF_users_created_at]
    DEFAULT (GETDATE()),
    [updated_at] DATETIME2(0)  NOT NULL CONSTRAINT [DF_users_updated_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_users] PRIMARY KEY CLUSTERED ([user_id] ASC),
    CONSTRAINT [UQ_users_username] UNIQUE ([username])
    );
GO

-- Create an index on email (non-clustered)
CREATE NONCLUSTERED INDEX [idx_email]
    ON [dbo].[users]([email]);
GO

-------------------------------------------------------------------------------
-- 4. CREATE TABLE: all_plants
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[all_plants] (
    [plant_id]        BIGINT        NOT NULL IDENTITY(1,1),
    [plant_cultivar]  NVARCHAR(191) NOT NULL,
    [plant_species]   NVARCHAR(191) NOT NULL,
    [is_deleted]      BIT           NOT NULL CONSTRAINT [DF_all_plants_is_deleted]
    DEFAULT (0),
    [created_at]      DATETIME2(0)  NOT NULL CONSTRAINT [DF_all_plants_created_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_all_plants] PRIMARY KEY CLUSTERED ([plant_id] ASC),
    CONSTRAINT [UQ_all_plants_cultivar_species] UNIQUE ([plant_cultivar], [plant_species])
    );
GO

-- Create an index on plant_species (non-clustered)
CREATE NONCLUSTERED INDEX [idx_species]
    ON [dbo].[all_plants]([plant_species]);
GO

-------------------------------------------------------------------------------
-- 5. CREATE TABLE: user_plants
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[user_plants] (
    [user_plant_id] BIGINT       NOT NULL IDENTITY(1,1),
    [user_id]       BIGINT       NOT NULL,
    [plant_id]      BIGINT       NOT NULL,
    [planting_time] DATE         NOT NULL,
    [est_cropping]  TINYINT      NULL,
    [is_deleted]    BIT          NOT NULL CONSTRAINT [DF_user_plants_is_deleted]
    DEFAULT (0),
    [photo_url]     NVARCHAR(255) NULL,
    [created_at]    DATETIME2(0) NOT NULL CONSTRAINT [DF_user_plants_created_at]
    DEFAULT (GETDATE()),
    [updated_at]    DATETIME2(0) NOT NULL CONSTRAINT [DF_user_plants_updated_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_user_plants] PRIMARY KEY CLUSTERED ([user_plant_id] ASC),
    CONSTRAINT [FK_user_plants_users]
    FOREIGN KEY ([user_id]) REFERENCES [dbo].[users]([user_id]) ON DELETE CASCADE,
    CONSTRAINT [FK_user_plants_all_plants]
    FOREIGN KEY ([plant_id]) REFERENCES [dbo].[all_plants]([plant_id]) ON DELETE CASCADE
    );
GO

-- Create non-clustered indexes
CREATE NONCLUSTERED INDEX [idx_user_plants]
    ON [dbo].[user_plants]([user_id], [plant_id]);

CREATE NONCLUSTERED INDEX [idx_planting_time]
    ON [dbo].[user_plants]([planting_time]);
GO

-------------------------------------------------------------------------------
-- 6. CREATE TABLE: weather_data
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[weather_data] (
    [weather_id]  BIGINT        NOT NULL IDENTITY(1,1),
    [user_id]     BIGINT        NOT NULL,
    [location]    NVARCHAR(191) NULL,
    [temperature] DECIMAL(5, 2) NULL,
    [humidity]    DECIMAL(5, 2) NULL,
    [recorded_at] DATETIME2(0)  NOT NULL CONSTRAINT [DF_weather_data_recorded_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_weather_data] PRIMARY KEY CLUSTERED ([weather_id] ASC),
    CONSTRAINT [FK_weather_data_users]
    FOREIGN KEY ([user_id]) REFERENCES [dbo].[users]([user_id]) ON DELETE CASCADE
    );
GO

-- Create non-clustered indexes
CREATE NONCLUSTERED INDEX [idx_user_weather]
    ON [dbo].[weather_data]([user_id]);

CREATE NONCLUSTERED INDEX [idx_location]
    ON [dbo].[weather_data]([location]);

CREATE NONCLUSTERED INDEX [idx_recorded_at]
    ON [dbo].[weather_data]([recorded_at]);
GO

-------------------------------------------------------------------------------
-- 7. CREATE TABLE: soil_types
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[soil_types] (
    [soil_id]        BIGINT         NOT NULL IDENTITY(1,1),
    [soil_type]      NVARCHAR(50)   NOT NULL,
    [ph_range]       NVARCHAR(20)   NULL,
    [description]    NVARCHAR(MAX)  NULL,
    [suitable_plants] NVARCHAR(MAX) NULL,
    [created_at]     DATETIME2(0)   NOT NULL CONSTRAINT [DF_soil_types_created_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_soil_types] PRIMARY KEY CLUSTERED ([soil_id] ASC),
    CONSTRAINT [UQ_soil_types_soil_type] UNIQUE ([soil_type])
    );
GO

-------------------------------------------------------------------------------
-- 8. CREATE TABLE: growing_environments
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[growing_environments] (
    [environment_id] BIGINT       NOT NULL IDENTITY(1,1),
    [user_id]        BIGINT       NOT NULL,
    [soil_id]        BIGINT       NOT NULL,
    [name]           NVARCHAR(191) NOT NULL,
    [created_at]     DATETIME2(0) NOT NULL CONSTRAINT [DF_growing_env_created_at]
    DEFAULT (GETDATE()),
    [updated_at]     DATETIME2(0) NOT NULL CONSTRAINT [DF_growing_env_updated_at]
    DEFAULT (GETDATE()),
    CONSTRAINT [PK_growing_environments] PRIMARY KEY CLUSTERED ([environment_id] ASC),
    CONSTRAINT [FK_growing_environments_users]
    FOREIGN KEY ([user_id]) REFERENCES [dbo].[users]([user_id]) ON DELETE CASCADE,
    CONSTRAINT [FK_growing_environments_soil_types]
    FOREIGN KEY ([soil_id]) REFERENCES [dbo].[soil_types]([soil_id]) ON DELETE CASCADE
    );
GO

-- Create non-clustered index on (user_id)
CREATE NONCLUSTERED INDEX [idx_user_environments]
    ON [dbo].[growing_environments]([user_id]);
GO

-------------------------------------------------------------------------------
-- 9. CREATE TABLE: actuator_outputs
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[actuator_outputs] (
    [output_id]     BIGINT       NOT NULL IDENTITY(1,1),
    [actuator_name] NVARCHAR(50) NOT NULL,
    [state]         BIT          NOT NULL CONSTRAINT [DF_actuator_outputs_state]
    DEFAULT (0),
    [timestamp]     DATETIME2(0) NOT NULL CONSTRAINT [DF_actuator_outputs_timestamp]
    DEFAULT (GETDATE()),
    [environment_id] BIGINT      NOT NULL,
    CONSTRAINT [PK_actuator_outputs] PRIMARY KEY CLUSTERED ([output_id] ASC),
    CONSTRAINT [FK_actuator_outputs_growing_env]
    FOREIGN KEY ([environment_id]) REFERENCES [dbo].[growing_environments]([environment_id])
    ON DELETE CASCADE
    );
GO

-- Create non-clustered indexes
CREATE NONCLUSTERED INDEX [idx_environment_actuators]
    ON [dbo].[actuator_outputs]([environment_id]);

CREATE NONCLUSTERED INDEX [idx_actuator_timestamp]
    ON [dbo].[actuator_outputs]([timestamp]);
GO

-------------------------------------------------------------------------------
-- 10. CREATE TABLE: sensor_inputs
-------------------------------------------------------------------------------
CREATE TABLE [dbo].[sensor_inputs] (
    [input_id]      BIGINT       NOT NULL IDENTITY(1,1),
    [sensor_name]   NVARCHAR(50) NOT NULL,
    [sensor_type]   NVARCHAR(50) NULL,
    [value]         DECIMAL(10,2) NULL,
    [unit]          NVARCHAR(20)  NULL,
    [timestamp]     DATETIME2(0)  NOT NULL CONSTRAINT [DF_sensor_inputs_timestamp]
    DEFAULT (GETDATE()),
    [environment_id] BIGINT       NOT NULL,
    CONSTRAINT [PK_sensor_inputs] PRIMARY KEY CLUSTERED ([input_id] ASC),
    CONSTRAINT [FK_sensor_inputs_growing_env]
    FOREIGN KEY ([environment_id]) REFERENCES [dbo].[growing_environments]([environment_id])
    ON DELETE CASCADE
    );
GO

-- Create non-clustered indexes
CREATE NONCLUSTERED INDEX [idx_environment_sensors]
    ON [dbo].[sensor_inputs]([environment_id]);

CREATE NONCLUSTERED INDEX [idx_sensor_timestamp]
    ON [dbo].[sensor_inputs]([timestamp]);
GO

-------------------------------------------------------------------------------
-- 11. SAMPLE DATA INSERTS
-------------------------------------------------------------------------------
INSERT INTO [dbo].[users] ([username], [password], [email])
VALUES
    (N'joonas', N'$2b$10$H0/fwx3mnPLuNLve4k74CO44iZy8MZ8KSJbvWwchgpjqeNVBIi6Xe', N'<EMAIL>'),
    (N'test',   N'$2b$10$p.4iOlmdZ.muJouF0ppY.OPxJU5I23lta4eS5ESIfAqcb/k55Pqx6',   N'<EMAIL>');
GO

INSERT INTO [dbo].[all_plants] ([plant_cultivar], [plant_species])
VALUES
    (N'Tomato', N'Solanum lycopersicum'),
    (N'Basil',  N'Ocimum basilicum');
GO

INSERT INTO [dbo].[user_plants] (
    [user_id],
    [plant_id],
    [planting_time],
    [est_cropping],
[photo_url]
)
VALUES
    (1, 1, '2025-01-01', 60, N'/uploads/tomato.jpg'),
    (1, 2, '2025-01-05', 45, N'/uploads/basil.jpg');
GO
