{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js"}, "repository": {"type": "git", "url": "git+https://github.com/JoonasMagi/AiAbimees.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/JoonasMagi/AiAbimees/issues"}, "homepage": "https://github.com/JoonasMagi/AiAbimees#readme", "devDependencies": {"nodemon": "^3.1.9"}, "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "csrf": "^3.1.0", "csurf": "^1.10.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.0.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "path": "^0.12.7", "typescript": "^5.7.3"}}