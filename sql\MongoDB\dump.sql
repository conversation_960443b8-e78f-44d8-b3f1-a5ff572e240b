use studentdb

db.createCollection('users', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['username', 'password'],
            properties: {
                username: {
                    bsonType: 'string',
                    description: 'Must be a string and is required'
                },
                password: {
                    bsonType: 'string',
                    description: 'Must be a string and is required'
                },
                email: {
                    bsonType: 'string',
                    description: 'Must be a string if the field exists'
                },
                created_at: {
                    bsonType: 'date',
                    description: 'Stores the user creation date'
                },
                updated_at: {
                    bsonType: 'date',
                    description: 'Stores the user update date'
                }
            }
        }
    }
})
db.users.createIndex({username: 1}, {unique: true})
db.users.createIndex({email: 1})
db.users.insertMany([
    {
        username: 'joonas',
        password: '$2b$10$H0/fwx3mnPLuNLve4k74CO44iZy8MZ8KSJbvWwchgpjqeNVBIi6Xe',
        email: '<EMAIL>',
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        username: 'test',
        password: '$2b$10$p.4iOlmdZ.muJouF0ppY.OPxJU5I23lta4eS5ESIfAqcb/k55Pqx6',
        email: '<EMAIL>',
        created_at: new Date(),
        updated_at: new Date()
    }
])
db.createCollection('all_plants', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['plant_cultivar', 'plant_species'],
            properties: {
                plant_cultivar: {bsonType: 'string'},
                plant_species: {bsonType: 'string'},
                is_deleted: {bsonType: 'bool'},
                created_at: {bsonType: 'date'}
            }
        }
    }
})

// Indexes
db.all_plants.createIndex({plant_cultivar: 1, plant_species: 1}, {unique: true})
db.all_plants.createIndex({plant_species: 1})
db.all_plants.insertMany([
    {
        plant_cultivar: 'Tomato',
        plant_species: 'Solanum lycopersicum',
        is_deleted: false,
        created_at: new Date()
    },
    {
        plant_cultivar: 'Basil',
        plant_species: 'Ocimum basilicum',
        is_deleted: false,
        created_at: new Date()
    }
])
db.createCollection('user_plants', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['user_id', 'plant_id', 'planting_time'],
            properties: {
                user_id: {
                    bsonType: 'objectId',
                    description: 'Reference to _id from users collection'
                },
                plant_id: {
                    bsonType: 'objectId',
                    description: 'Reference to _id from all_plants collection'
                },
                planting_time: {bsonType: 'date'},
                est_cropping: {bsonType: 'int'},
                is_deleted: {bsonType: 'bool'},
                photo_url: {bsonType: 'string'},
                created_at: {bsonType: 'date'},
                updated_at: {bsonType: 'date'}
            }
        }
    }
})

// Indexes (optional, based on usage)
db.user_plants.createIndex({user_id: 1, plant_id: 1})
db.user_plants.createIndex({planting_time: 1})
const joonasId = ObjectId("605a1c2d14f5c41d245746dd")
const testUserId = ObjectId("605a1c2d14f5c41d245746de")
const tomatoId = ObjectId("605a1c2d14f5c41d245746df")
const basilId = ObjectId("605a1c2d14f5c41d245746e0")

db.user_plants.insertMany([
    {
        user_id: joonasId,
        plant_id: tomatoId,
        planting_time: new Date("2025-01-01"),
        est_cropping: 60,
        is_deleted: false,
        photo_url: "/uploads/tomato.jpg",
        created_at: new Date(),
        updated_at: new Date()
    },
    {
        user_id: joonasId,
        plant_id: basilId,
        planting_time: new Date("2025-01-05"),
        est_cropping: 45,
        is_deleted: false,
        photo_url: "/uploads/basil.jpg",
        created_at: new Date(),
        updated_at: new Date()
    }
])
db.createCollection('weather_data', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['user_id'],
            properties: {
                user_id: {bsonType: 'objectId'},
                location: {bsonType: 'string'},
                temperature: {bsonType: 'decimal'},
                humidity: {bsonType: 'decimal'},
                recorded_at: {bsonType: 'date'}
            }
        }
    }
})

// Indexes
db.weather_data.createIndex({user_id: 1})
db.weather_data.createIndex({location: 1})
db.weather_data.createIndex({recorded_at: 1})
db.weather_data.insertOne({
    user_id: joonasId,
    location: "Somewhere in Estonia",
    temperature: NumberDecimal("20.50"),
    humidity: NumberDecimal("60.00"),
    recorded_at: new Date()
})
db.createCollection('soil_types', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['soil_type'],
            properties: {
                soil_type: {bsonType: 'string'},
                ph_range: {bsonType: 'string'},
                description: {bsonType: 'string'},
                suitable_plants: {bsonType: 'string'},
                created_at: {bsonType: 'date'}
            }
        }
    }
})

// Index
db.soil_types.createIndex({soil_type: 1}, {unique: true})
db.createCollection('growing_environments', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['user_id', 'soil_id', 'name'],
            properties: {
                user_id: {bsonType: 'objectId'},
                soil_id: {bsonType: 'objectId'},
                name: {bsonType: 'string'},
                created_at: {bsonType: 'date'},
                updated_at: {bsonType: 'date'}
            }
        }
    }
})

db.growing_environments.createIndex({user_id: 1})
db.createCollection('actuator_outputs', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['actuator_name', 'environment_id'],
            properties: {
                actuator_name: {bsonType: 'string'},
                state: {bsonType: 'bool'},
                timestamp: {bsonType: 'date'},
                environment_id: {bsonType: 'objectId'}
            }
        }
    }
})

db.actuator_outputs.createIndex({environment_id: 1})
db.actuator_outputs.createIndex({timestamp: 1})
db.createCollection('sensor_inputs', {
    validator: {
        $jsonSchema: {
            bsonType: 'object',
            required: ['sensor_name', 'environment_id'],
            properties: {
                sensor_name: {bsonType: 'string'},
                sensor_type: {bsonType: 'string'},
                value: {bsonType: 'decimal'},
                unit: {bsonType: 'string'},
                timestamp: {bsonType: 'date'},
                environment_id: {bsonType: 'objectId'}
            }
        }
    }
})

db.sensor_inputs.createIndex({environment_id: 1})
db.sensor_inputs.createIndex({timestamp: 1})

// Example objects (if needed, otherwise remove them)
const exampleUsers = [
    {
        _id: ObjectId("605a1c2d14f5c41d245746dd"),
        username: "joonas",
        password: "$2b$10$H0/fwx3mnPLuNLve4k74CO...",
        email: "<EMAIL>",
        created_at: ISODate("2025-01-01T00:00:00Z"),
        updated_at: ISODate("2025-01-01T00:00:00Z")
    }
]

const examplePlants = [
    {
        _id: ObjectId("605a1c2d14f5c41d245746df"),
        plant_cultivar: "Tomato",
        plant_species: "Solanum lycopersicum",
        is_deleted: false,
        created_at: ISODate("2025-01-01T00:00:00Z")
    }
]

const exampleUserPlants = [
    {
        _id: ObjectId("someObjectId"),
        user_id: ObjectId("605a1c2d14f5c41d245746dd"),   // references users._id
        plant_id: ObjectId("605a1c2d14f5c41d245746df"),  // references all_plants._id
        planting_time: ISODate("2025-01-01T00:00:00Z"),
        est_cropping: 60,
        is_deleted: false,
        photo_url: "/uploads/tomato.jpg",
        created_at: ISODate("2025-01-01T00:00:00Z"),
        updated_at: ISODate("2025-01-02T00:00:00Z")
    }
]

use studentdbs