-- Drop and recreate the database (run from a different database, e.g. 'postgres')
DROP DATABASE IF EXISTS aiabimees;
CREATE DATABASE aiabimees;

-- Connect to the newly created database
\c aiabimees;

-- Table: users
CREATE TABLE users (
                       user_id      BIGSERIAL PRIMARY KEY,
                       username     VARCHAR(191) NOT NULL,
                       password     VARCHAR(191) NOT NULL,
                       email        VARCHAR(191),
                       created_at   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                       updated_at   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create a trigger or use an ON UPDATE expression for updated_at
-- In PostgreSQL, you'll typically handle updated_at with a trigger, or explicitly in your DML.
-- For a simple approach, you can do something like:
--   updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- Then rely on your application or an explicit trigger to update it.
--
-- If you want an automatic update, you'd do something like:
--   CREATE FUNCTION set_current_timestamp() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
--   BEGIN
--       NEW.updated_at = now();
--       R<PERSON>URN NEW;
--   END;
--   $$ LANGUAGE plpgsql;
--
--   CREATE TRIGGER update_users_updated_at
--   BEFORE UPDATE ON users
--   FOR EACH ROW
--   EXECUTE PROCEDURE set_current_timestamp();
--
-- Or simply manage it in your application layer.

-- Unique index on username
CREATE UNIQUE INDEX idx_username ON users(username);

-- Index on email
CREATE INDEX idx_email ON users(email);

------------------------------------------------------------------------

-- Table: all_plants
CREATE TABLE all_plants (
                            plant_id       BIGSERIAL PRIMARY KEY,
                            plant_cultivar VARCHAR(191) NOT NULL,
                            plant_species  VARCHAR(191) NOT NULL,
                            is_deleted     BOOLEAN NOT NULL DEFAULT FALSE,
                            created_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Unique index on (plant_cultivar, plant_species)
CREATE UNIQUE INDEX idx_cultivar_species ON all_plants(plant_cultivar, plant_species);
-- Index on plant_species
CREATE INDEX idx_species ON all_plants(plant_species);

------------------------------------------------------------------------

-- Table: user_plants
CREATE TABLE user_plants (
                             user_plant_id  BIGSERIAL PRIMARY KEY,
                             user_id        BIGINT NOT NULL,
                             plant_id       BIGINT NOT NULL,
                             planting_time  DATE NOT NULL,
                             est_cropping   SMALLINT,
                             is_deleted     BOOLEAN NOT NULL DEFAULT FALSE,
                             photo_url      VARCHAR(255),
                             created_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             updated_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Foreign keys
ALTER TABLE user_plants
    ADD CONSTRAINT fk_user_plants_user
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE user_plants
    ADD CONSTRAINT fk_user_plants_plant
        FOREIGN KEY (plant_id) REFERENCES all_plants(plant_id) ON DELETE CASCADE;

-- Additional indexes
CREATE INDEX idx_user_plants ON user_plants(user_id, plant_id);
CREATE INDEX idx_planting_time ON user_plants(planting_time);

------------------------------------------------------------------------

-- Table: weather_data
CREATE TABLE weather_data (
                              weather_id   BIGSERIAL PRIMARY KEY,
                              user_id      BIGINT NOT NULL,
                              location     VARCHAR(191),
                              temperature  DECIMAL(5, 2),
                              humidity     DECIMAL(5, 2),
                              recorded_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE weather_data
    ADD CONSTRAINT fk_user_weather
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

CREATE INDEX idx_user_weather ON weather_data(user_id);
CREATE INDEX idx_location ON weather_data(location);
CREATE INDEX idx_recorded_at ON weather_data(recorded_at);

------------------------------------------------------------------------

-- Table: soil_types
CREATE TABLE soil_types (
                            soil_id        BIGSERIAL PRIMARY KEY,
                            soil_type      VARCHAR(50) NOT NULL,
                            ph_range       VARCHAR(20),
                            description    TEXT,
                            suitable_plants TEXT,
                            created_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Unique index on soil_type
CREATE UNIQUE INDEX idx_soil_type ON soil_types(soil_type);

------------------------------------------------------------------------

-- Table: growing_environments
CREATE TABLE growing_environments (
                                      environment_id BIGSERIAL PRIMARY KEY,
                                      user_id        BIGINT NOT NULL,
                                      soil_id        BIGINT NOT NULL,
                                      name           VARCHAR(191) NOT NULL,
                                      created_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      updated_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE growing_environments
    ADD CONSTRAINT fk_user_environments
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE growing_environments
    ADD CONSTRAINT fk_environment_soil
        FOREIGN KEY (soil_id) REFERENCES soil_types(soil_id) ON DELETE CASCADE;

CREATE INDEX idx_user_environments ON growing_environments(user_id);

------------------------------------------------------------------------

-- Table: actuator_outputs
CREATE TABLE actuator_outputs (
                                  output_id      BIGSERIAL PRIMARY KEY,
                                  actuator_name  VARCHAR(50) NOT NULL,
                                  state          BOOLEAN NOT NULL DEFAULT FALSE,
                                  "timestamp"    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  environment_id BIGINT NOT NULL
);

ALTER TABLE actuator_outputs
    ADD CONSTRAINT fk_environment_actuators
        FOREIGN KEY (environment_id) REFERENCES growing_environments(environment_id) ON DELETE CASCADE;

CREATE INDEX idx_environment_actuators ON actuator_outputs(environment_id);
CREATE INDEX idx_actuator_timestamp ON actuator_outputs("timestamp");

------------------------------------------------------------------------

-- Table: sensor_inputs
CREATE TABLE sensor_inputs (
                               input_id       BIGSERIAL PRIMARY KEY,
                               sensor_name    VARCHAR(50) NOT NULL,
                               sensor_type    VARCHAR(50),
                               value          DECIMAL(10, 2),
                               unit           VARCHAR(20),
                               "timestamp"    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               environment_id BIGINT NOT NULL
);

ALTER TABLE sensor_inputs
    ADD CONSTRAINT fk_environment_sensors
        FOREIGN KEY (environment_id) REFERENCES growing_environments(environment_id) ON DELETE CASCADE;

CREATE INDEX idx_environment_sensors ON sensor_inputs(environment_id);
CREATE INDEX idx_sensor_timestamp ON sensor_inputs("timestamp");

------------------------------------------------------------------------

-- Sample Data for Testing
INSERT INTO users (username, password, email)
VALUES
    ('joonas', '$2b$10$H0/fwx3mnPLuNLve4k74CO44iZy8MZ8KSJbvWwchgpjqeNVBIi6Xe', '<EMAIL>'),
    ('test',   '$2b$10$p.4iOlmdZ.muJouF0ppY.OPxJU5I23lta4eS5ESIfAqcb/k55Pqx6',   '<EMAIL>');

INSERT INTO all_plants (plant_cultivar, plant_species)
VALUES
    ('Tomato', 'Solanum lycopersicum'),
    ('Basil',  'Ocimum basilicum');

INSERT INTO user_plants (user_id, plant_id, planting_time, est_cropping, photo_url)
VALUES
    (1, 1, '2025-01-01', 60, '/uploads/tomato.jpg'),
    (1, 2, '2025-01-05', 45, '/uploads/basil.jpg');
