/* Variables */
:root {
    --primary-color: #4CAF50;
    --primary-dark: #388E3C;
    --error-color: #f44336;
    --success-color: #4CAF50;
    --warning-color: #ff9800;
}

/* Header styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.header-title {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.header-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

#auth-btn,
#new-plant-btn,
#logout-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

#logout-btn {
    background-color: var(--error-color);
}

#auth-btn:hover,
#new-plant-btn:hover {
    background-color: var(--primary-dark);
}

#logout-btn:hover {
    background-color: #d32f2f;
}

/* Main content */
.main-content {
    margin-top: 80px;
    padding: 1rem;
}

/* Accessibility */
.skip-link {
    position: absolute;
    left: -9999px;
    z-index: 999;
    padding: 1em;
    background-color: white;
    text-decoration: none;
}

.skip-link:focus {
    left: 50%;
    transform: translateX(-50%);
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: white;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.close-button {
    position: absolute;
    right: 1rem;
    top: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    line-height: 1;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input[type="text"],
input[type="password"],
input[type="date"],
input[type="number"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

input[type="file"] {
    width: 100%;
    padding: 0.5rem 0;
}

button {
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

button:hover {
    background-color: var(--primary-dark);
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Error message */
.error-message {
    display: none;
    background-color: var(--error-color);
    color: white;
    padding: 0.75rem;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
}

/* Plants list */
.plants-list {
    display: list-item;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

/* Loading state */
.loading-state {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #666;
}

/* Link button */
.link-button {
    background: none;
    border: none;
    padding: 0;
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;
}

.link-button:hover {
    color: var(--primary-dark);
    background: none;
}

/* Utility classes */
.hidden {
    display: none !important;
}
.plant-item {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.plant-item img {
    width: 75px;
    height: 100px;
    border-radius: 1%;
    object-fit: cover;
    margin-right: 20px;
}

.plant-info {
    display: flex;
    flex-direction: column;
}

.plant-name {
    font-size: 20px;
    font-weight: bold;
    color: #222;
}

.plant-species {
    font-size: 15px;
    color: #555;
}

.delete-plant-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--error-color);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0;
    font-size: 1.25rem;
    z-index: 2; /* Ensures button stays above other elements */
}
.delete-section {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

.delete-btn {
    background-color: var(--error-color);
    color: white;
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

.delete-btn:hover {
    background-color: #d32f2f;
}